import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';

import { UserManagementService } from '../../Services/UserManagement.service';
import { NotificationService } from '../../../../Core/Services/notification.service';
import {
  UserDetails,
  AddUserRequest,
  UpdateUserRequest,
  LoadingStates,
  ErrorStates,
} from '../../Models/UserManagement';

@Component({
  selector: 'app-add-edit-user',
  standalone: false,
  templateUrl: './add-edit-user.component.html',
  styleUrl: './add-edit-user.component.scss',
})
export class AddEditUserComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();

  userForm!: FormGroup;
  isEditMode = false;
  userId: string | null = null;
  pageTitle = 'Add User';

  // Available roles for selection
  availableRoles = [
    { value: 'User', label: 'User' },
    { value: 'Admin', label: 'Admin' },
    { value: 'Super Admin', label: 'Super Admin' },
  ];

  // Default passwords based on roles
  private readonly defaultPasswords = {
    User: 'User123@',
    Admin: 'Admin123@',
    'Super Admin': 'Superadmin123@',
  };

  // State management
  loadingStates: LoadingStates = {
    fetchingUsers: false,
    deletingUser: false,
    updatingUser: false,
    addingUser: false,
  };

  errorStates: ErrorStates = {
    fetchError: null,
    deleteError: null,
    updateError: null,
    addError: null,
  };

  constructor(
    private fb: FormBuilder,
    private route: ActivatedRoute,
    private router: Router,
    private userManagementService: UserManagementService,
    private notificationService: NotificationService,
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.setupSubscriptions();
    this.checkRouteParams();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  private initializeForm(): void {
    this.userForm = this.fb.group({
      fullName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [
        '',
        [
          Validators.required,
          Validators.pattern('^[0-9]*$'),
          Validators.minLength(10),
          Validators.maxLength(15),
        ],
      ], // Required 10-digit phone number
      description: ['', [Validators.maxLength(50)]],
      isActive: [true],
      role: ['', [Validators.required]],
    });
  }

  private setupSubscriptions(): void {
    // Subscribe to loading states
    this.userManagementService.loadingStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.loadingStates = states;
      });

    // Subscribe to error states
    this.userManagementService.errorStates$
      .pipe(takeUntil(this.destroy$))
      .subscribe((states) => {
        this.errorStates = states;
        this.handleErrors(states);
      });
  }

  private checkRouteParams(): void {
    this.userId = this.route.snapshot.paramMap.get('id');
    if (this.userId) {
      this.isEditMode = true;
      this.pageTitle = 'Edit User';
      this.loadUserData(this.userId);
      // Remove password validation for edit mode
      this.userForm.get('password')?.clearValidators();
      this.userForm.get('confirmPassword')?.clearValidators();
      this.userForm.updateValueAndValidity();
    }
  }

  private loadUserData(userId: string): void {
    this.userManagementService
      .getUserById(userId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (user: UserDetails) => {
          this.populateForm(user);
        },
        error: (error) => {
          this.showError(`Failed to load user data: ${error.message}`);
          this.router.navigate(['/user-management/list']);
        },
      });
  }

  private populateForm(user: UserDetails): void {
    // Get the first role from the roles array for single selection
    const primaryRole =
      user.roles && user.roles.length > 0 ? user.roles[0] : '';

    // Ensure the role matches one of our available roles (case-sensitive)
    const matchedRole = this.availableRoles.find(
      (role) => role.value === primaryRole,
    );

    const roleToSet = matchedRole ? primaryRole : '';

    this.userForm.patchValue({
      fullName: user.fullName || '',
      email: user.email || '',
      phoneNumber: user.phoneNumber || '',
      description: user.description || '',
      isActive: user.isActive,
      role: roleToSet,
    });

    // Mark form as pristine after loading data
    this.userForm.markAsPristine();
  }

  private getDefaultPassword(role: string): string {
    return (
      this.defaultPasswords[role as keyof typeof this.defaultPasswords] ||
      'User123@'
    );
  }

  getRoleIcon(role: string): string {
    const roleIcons = {
      User: 'person',
      Admin: 'admin_panel_settings',
      'Super Admin': 'supervisor_account',
    };
    return roleIcons[role as keyof typeof roleIcons] || 'person';
  }

  onSubmit(): void {
    if (this.userForm.valid) {
      if (this.isEditMode) {
        this.updateUser();
      } else {
        this.addUser();
      }
    } else {
      this.markFormGroupTouched();
      this.showFormValidationErrors();
    }
  }

  private showFormValidationErrors(): void {
    const errors: string[] = [];

    if (this.userForm.get('fullName')?.hasError('required')) {
      errors.push('Full name is required');
    }
    if (this.userForm.get('fullName')?.hasError('minlength')) {
      errors.push('Full name must be at least 2 characters');
    }
    if (this.userForm.get('email')?.hasError('required')) {
      errors.push('Email is required');
    }
    if (this.userForm.get('email')?.hasError('email')) {
      errors.push('Please enter a valid email address');
    }
    if (this.userForm.get('phoneNumber')?.hasError('required')) {
      errors.push('Phone number is required');
    }
    if (this.userForm.get('phoneNumber')?.hasError('pattern')) {
      errors.push('Phone number must be exactly 10 digits');
    }
    if (
      this.userForm.get('phoneNumber')?.hasError('minlength') ||
      this.userForm.get('phoneNumber')?.hasError('maxlength')
    ) {
      errors.push('Phone number must be exactly 10 digits long');
    }
    if (this.userForm.get('role')?.hasError('required')) {
      errors.push('Role is required');
    }

    if (errors.length > 0) {
      this.showError(`Please fix the following errors: ${errors.join(', ')}`);
    }
  }

  private addUser(): void {
    const formValue = this.userForm.value;
    const defaultPassword = this.getDefaultPassword(formValue.role);

    const addUserRequest: AddUserRequest = {
      email: formValue.email,
      fullName: formValue.fullName,
      password: defaultPassword, // Use default password based on role
      isActive: formValue.isActive,
      phoneNumber: formValue.phoneNumber || undefined,
      description: formValue.description || undefined,
      roles: [formValue.role], // Convert single role to array for backend
    };

    // Debug logging to verify roles are being sent
    console.log('Add User Request:', addUserRequest);
    console.log('Selected Role:', formValue.role);
    console.log('Roles Array:', addUserRequest.roles);

    this.userManagementService.addUser(addUserRequest).subscribe({
      next: (response) => {
        console.log('User creation response:', response);
        this.showSuccess(
          `User created successfully with default password for ${formValue.role} role`,
        );
        this.router.navigate(['/user-management/list']);
      },
      error: (error) => {
        console.error('Error creating user:', error);
        this.showError(`Failed to create user: ${error.message}`);
      },
    });
  }

  private updateUser(): void {
    if (!this.userId) return;

    const formValue = this.userForm.value;
    const updateUserRequest: UpdateUserRequest = {
      id: this.userId,
      fullName: formValue.fullName,
      email: formValue.email,
      isActive: formValue.isActive,
      phoneNumber: formValue.phoneNumber || undefined,
      description: formValue.description || undefined,
      roles: [formValue.role], // Convert single role to array for backend
    };

    this.userManagementService
      .updateUser(this.userId, updateUserRequest)
      .subscribe({
        next: () => {
          this.showSuccess('User updated successfully');
          this.router.navigate(['/user-management/list']);
        },
        error: (error) => {
          console.error('Error updating user:', error);
          this.showError(`Failed to update user: ${error.message}`);
        },
      });
  }

  onCancel(): void {
    this.router.navigate(['/user-management/list']);
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach((key) => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  private handleErrors(errorStates: ErrorStates): void {
    if (errorStates.addError) {
      this.showError(`Failed to create user: ${errorStates.addError}`);
    }
    if (errorStates.updateError) {
      this.showError(`Failed to update user: ${errorStates.updateError}`);
    }
  }

  private showError(message: string): void {
    this.notificationService.showError(message);
  }

  private showSuccess(message: string): void {
    this.notificationService.showSuccess(message);
  }

  // Getter methods for template
  get isLoading(): boolean {
    return this.loadingStates.addingUser || this.loadingStates.updatingUser;
  }

  get formTitle(): string {
    return this.isEditMode ? 'Edit User' : 'Add New User';
  }

  get submitButtonText(): string {
    if (this.isLoading) {
      return this.isEditMode ? 'Updating...' : 'Creating...';
    }
    return this.isEditMode ? 'Update User' : 'Create User';
  }
}
